{% extends 'base.html' %}

{% block title %}
  PLA Map (Flask Leaflet)
{% endblock %}

{% block main %}

    <div id="map"></div>

    <script>

      // My Leaflet map
      var map = L.map('map').setView([
        36.384646,
        94.797023,
        ], 3);

        // Add OpenStreetMap
        // L.tileLayer('http://cartodb.orrrr.local:8989/tile/base-light/{z}/{x}/{y}.png', {  # HIGH Local Map Server Example
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {

        // Hide the footer message for now
        // attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
      }).addTo(map);
      
      {% for marker in markers %}
        L.marker([{{ marker['lat'] }}, {{ marker['lon'] }}],
          {
            icon: L.icon({
              iconUrl: "{{ url_for('static', filename='dataset/icons/' + marker['styleUrl'] + '.png') }}",
              iconSize: [32, 32],
              iconAnchor: [16, 32]
            })
          }
        ).addTo(map)
        .bindPopup("{{ marker['popup'] }}")

      {% endfor %}

    </script>

    <div class="bottom-area">

      <!-- include the jquery  -->
      <script src="{{ url_for('static', filename='js/jquery-3.7.1.min.js') }}"></script>

      <!-- Make a Jquery Search that will update the table and filter as the user types letters that are in the name or description (using vanilla javascript dom manipulation only, after the page is fully loaded) -->

      <script>
        $(document).ready(function() {
          $("#search").on("keyup", function() {
            var value = $(this).val().toLowerCase();
            $("table tr").filter(function() {
              $(this).toggle($(this).text().toLowerCase().indexOf(value) > -1);
            });
          });
        });
      </script>

      <!-- Center this searchbox, and style is nicely and round -->
      <div class="searchbox">
        <!-- Text to the left of the searchbox to instruct the user to enter text to filter on -->
        <input id="search" type="text" placeholder="Enter text to filter on (name or description)..." autofocus style="margin: 0 auto; display: block; width: 35%; margin-top: 10px; margin-bottom: 10px; border-radius: 10px;">

      </div>

      <br>

      <!-- make this table autofit and fill up the bottom area, and highlight the row when you mouseover it -->
      <!-- make a bit of spacing to the left and right of the table, and also a bit of spacing above the table -->
      <table class="table table-hover table-condensed table-bordered" style="width: 98%; margin: 0 auto; margin-top: 10px;">
        <tr>
          <th>Name</th>
          <th>Description</th>
          <th>Lat</th>
          <th>Lon</th>
        </tr>
        {% for marker in markers %}
          <tr>
            <!-- TODO need to make the max length of name/description 20 chars (use jinja2 slice, or adjust it before returning?) -->
            <!-- Maybe somethign like  this... map.flyTo([lat, lng], zoom);  -->
            <td><a href="#" onclick="map.flyTo([{{ marker['lat'] }}, {{ marker['lon'] }}], 10);">{{ marker['name'] }}</a></td>
            <td>{{ marker['description'] }}</td>
            <td>{{ marker['lat'] }}</td>
            <td>{{ marker['lon'] }}</td>
          </tr>
        {% endfor %}
      </table>
    </div>

{% endblock %}

