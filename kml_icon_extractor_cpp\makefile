PROGRAM  = $(BINDIR)/icon-extractor
OBJDIR   = obj
BINDIR   = bin
$(shell mkdir -p $(OBJDIR) $(BINDIR))

CC       = g++
CFLAGS   = -Wall -std=c++11
LINC     =
LIBS     = -lxerces-c
OBJS     = kml_item.cpp \
           kml_data_map.cpp \
           kml_parser.cpp \
           test.cpp

ALLOBJS = $(OBJS:%.cpp=$(OBJDIR)/%.o)

ALL: $(PROGRAM)

debug: CFLAGS += -DDEBUG -g
debug: $(PROGRAM)

$(PROGRAM): $(ALLOBJS)
	$(CC) $(CFLAGS) -o $@ $(ALLOBJS) $(LINC) $(LIBS)

$(OBJDIR)/%.o : %.cpp
	$(CC) $(CFLAGS) -c $*.cpp -o $(OBJDIR)/$*.o

clean:
	rm -rf $(OBJDIR) $(BINDIR)
