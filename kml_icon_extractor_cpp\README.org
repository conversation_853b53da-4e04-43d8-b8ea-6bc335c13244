SAX parse the KML since it is a large file. DOM will be much slower and use a lot of overhead memory to hold all the data.

The goal was to do it in rust, but since we need it soon, will do in c++ and rewrite later in rust as it would be a good learning exercise.

* Dependencies
** boost headers and libraries
    sudo pacman -S boost
** xerces-c
    If Arch:
    sudo pacman -S xerces-c

    If Mobaxterm, use (the below to get boost+xerces a.i.o.s.): 
    apt-cyg install fish # (or apt, depends on the MobaXterm version)
    # Next, loop through the dependencies and install in moba (takes 2-3 hours... ugh)
    for i in `echo libxerces-c-devel libxerces-c31 mingw64-i686-xerces-c mingw64-x86_64-xerces-c xerces-c-devel cygwin64-gcc-g++ djgpp-gcc-g++ gcc-g++ gcc-mingw-g++ gcc4-g++ mingw64-i686-gcc-g++ mingw64-x86_64-gcc-g+ mingw64-i686-boost mingw64-x86_64-boost boost-build boost-devel ibus-typing-booster libboost-devel libboost_atomic1.60 libboost_atomic1.63 libboost_atomic1.64 libboost_atomic1.66 libboost_chrono1.60 libboost_chrono1.63 libboost_chrono1.64 libboost_chrono1.66 libboost_container1.60 libboost_container1.63 libboost_container1.64 libboost_container1.66 libboost_context1.60 libboost_context1.63 libboost_context1.64 libboost_context1.66 libboost_coroutine1.60 libboost_coroutine1.63 libboost_coroutine1.64 libboost_coroutine1.66 libboost_date_time1.60 libboost_date_time1.63 libboost_date_time1.64 libboost_date_time1.66 libboost_fiber1.64 libboost_filesystem1.60 libboost_filesystem1.63 libboost_filesystem1.64 libboost_filesystem1.66 libboost_graph1.60 libboost_graph1.63 libboost_graph1.64 libboost_graph1.66 libboost_graph_parallel1.63 libboost_graph_parallel1.64 libboost_graph_parallel1.66 libboost_iostreams1.60 libboost_iostreams1.63 libboost_iostreams1.64 libboost_iostreams1.66 libboost_locale1.60 libboost_locale1.63 libboost_locale1.64 libboost_locale1.66 libboost_log1.60 libboost_log1.63 libboost_log1.64 libboost_log1.66 libboost_math1.60 libboost_math1.63 libboost_math1.64 libboost_math1.66 libboost_mpi-devel libboost_mpi1.63 libboost_mpi1.64 libboost_mpi1.66 libboost_mpi_python1.63 libboost_mpi_python1.64 libboost_mpi_python1.66 libboost_mpi_python3_1.63 libboost_mpi_python3_1.64 libboost_mpi_python3_1.66 libboost_numpy1.63 libboost_numpy1.64 libboost_numpy1.66 libboost_numpy3_1.63 libboost_numpy3_1.64 libboost_numpy3_1.66 libboost_program_options1.60 libboost_program_options1.63 libboost_program_options1.64 libboost_program_options1.66 libboost_python-devel libboost_python1.60 libboost_python1.63 libboost_python1.64 libboost_python1.66 libboost_python3-devel libboost_python3_1.60 libboost_python3_1.63 libboost_python3_1.64 libboost_python3_1.66 libboost_random1.60 libboost_random1.63 libboost_random1.64 libboost_random1.66 libboost_regex1.60 libboost_regex1.63 libboost_regex1.64 libboost_regex1.66 libboost_serialization1.60 libboost_serialization1.63 libboost_serialization1.64 libboost_serialization1.66 libboost_signals1.60 libboost_signals1.63 libboost_signals1.64 libboost_signals1.66 libboost_stacktrace1.66 libboost_system1.60 libboost_system1.63 libboost_system1.64 libboost_system1.66 libboost_test1.60 libboost_test1.63 libboost_test1.64 libboost_test1.66 libboost_thread1.60 libboost_thread1.63 libboost_thread1.64 libboost_thread1.66 libboost_timer1.60 libboost_timer1.63 libboost_timer1.64 libboost_timer1.66 libboost_type_erasure1.60 libboost_type_erasure1.63 libboost_type_erasure1.64 libboost_type_erasure1.66 libboost_wave1.60 libboost_wave1.63 libboost_wave1.64 libboost_wave1.66 opencv python2-boost.mpi python3-boost.mpi make ` ; do apt-cyg install $i ; done

* Mapping
** kml id as key
Not sure if this is right, but current implementation uses the kml:id in the <gx:CascadingStyle> as they key

* Build

#+begin_example
make
#+end_example

* Run

#+begin_example
bin/icon-extractor /path/to/kmlFile
#+end_example

 After that has been run, there will be an icons directory created with all PNGs





* Misc Notes

** SAX Meaning:

#+BEGIN_SRC 

Prompt: what does SAX parse mean?

SAX stands for Simple API for XML. It is an event-driven, pull-based parsing API for XML 
documents. This means that the parser generates events as it parses the document, and the 
application developer can choose to handle or ignore these events as needed. This makes 
SAX a very flexible and efficient parsing API, but it also makes it more complex to use 
than DOM, another popular XML parsing API.

Here is a summary of the key features of SAX:

Event-driven: SAX generates events as it parses the document. This means that the 
application developer does not have to wait for the entire document to be parsed 
before they can start processing it.

Pull-based: SAX requires the application developer to pull data from the parser. This 
means that the application developer has more control over the parsing process, but it 
also means that they have to write more code.

Flexible: SAX is a very flexible parsing API. The application developer can choose to 
handle or ignore any of the events that are generated by the parser.

Efficient: SAX is a very efficient parsing API. This is because it does not have to 
load the entire document into memory before it can start processing it.


Here are some of the advantages of using SAX:

Efficient memory usage: SAX does not have to load the entire document into memory 
before it can start processing it. This makes it a good choice for parsing 
large XML documents.

Incremental processing: SAX allows the application developer to process the document 
as it is being parsed. This can be useful for applications that need to display or 
update data as it is being read from the XML document.

Customizable processing: SAX allows the application developer to customize the way 
that the document is processed. This can be useful for applications that need to 
extract specific data from the XML document.

Here are some of the disadvantages of using SAX:

Complexity: SAX is a more complex API to use than DOM. This is because the application 
developer has to write more code to handle the events that are generated by the parser.

Error handling: SAX is more difficult to use for error handling than DOM. This is 
because the application developer has to catch all of the exceptions that are thrown 
by the parser.

Overall, SAX is a powerful and flexible XML parsing API that is well-suited for a wide 
range of applications. However, it is a more complex API to use than DOM, and it 
requires more code from the application developer.
#+END_SRC