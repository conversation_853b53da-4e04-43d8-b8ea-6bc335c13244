#ifndef KML_DATA_H
#define KML_DATA_H

#include <string>
#include <cstdint>

class KmlItem
{
public:
   KmlItem(std::string filename, std::string value);
   ~KmlItem();

   void setValue(std::string value);
   void setFilename(std::string filename);

   const std::string getFilename() const;
   const std::string getValue() const;
   
private:
   std::string filename_;
   std::string value_;
};

inline void KmlItem::setValue(std::string value)
{
   this->value_ = value;
}

inline void KmlItem::setFilename(std::string filename)
{
   this->filename_ = filename;
}

inline const std::string KmlItem::getFilename() const
{
   return filename_;
}

inline const std::string KmlItem::getValue() const
{
   return value_;
}

#endif
