#ifndef KML_DATA_MAP_H
#define KML_DATA_MAP_H

#include <string>
#include <map>
#include "kml_item.h"

class KmlItem;

class KmlDataMap
{
public:
   KmlDataMap();
   ~KmlDataMap();

   void addEntry(std::string hash, std::string value);
   const std::map<std::string,KmlItem>& getMap() const;

private:
   std::map<std::string,KmlItem> map_;
};

inline const std::map<std::string,KmlItem>& KmlDataMap::getMap() const
{
   return map_;
}

#endif
