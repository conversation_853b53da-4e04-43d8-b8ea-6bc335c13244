import json
from flask import Flask, render_template, request
# from data import input_json
from data import input_kml as input_kml_string
import xml.etree.ElementTree as ET

input_kml = '''<?xml version="1.0" encoding="UTF-8"?>
<kml xmlns="http://www.opengis.net/kml/2.2" xmlns:gx="http://www.google.com/kml/ext/2.2" xmlns:kml="http://www.opengis.net/kml/2.2" xmlns:atom="http://www.w3.org/2005/Atom">
<Document id="19Q8BraU1Nmnk23TzMb5rhXFuIAnOpTTq">
	<name>test name</name>
	<description>test desc</description>

	<Schema name="__managed_schema" id="__managed_schema">
		<SimpleField type="string" name="str:5ZCN56ix">
			<displayName>名稱</displayName>
		</SimpleField>
		<SimpleField type="string" name="str:6Kqq5piO">
			<displayName>說明</displayName>
		</SimpleField>
	</Schema>

	<gx:CascadingStyle kml:id="__managed_style_22C08E9C3E2D1F66D1FC">
		<styleUrl>https://earth.google.com/balloon_components/base/********/card_template.kml#main</styleUrl>
		<Style>
			<IconStyle>
				<scale>1.2</scale>
				<Icon>
					<href>data:image/png;base64,iVBORw0KGgoAAaieoo/wv0BJaXErrzDCFxf11YU4K6FTtkslqprSqwSFWIp0Q7tKFr9RLHZR0ScU/0O9EOjEUpRK3o4iIiIiIiIiIiIiIiIiIiIiIiIiIiIiIPH7+D560Lrp58eDSAAAAAElFTkSuQmCC</href>
				</Icon>
			</IconStyle>
			<LabelStyle>
			</LabelStyle>
			<LineStyle>
				<color>ff000000</color>
				<width>1.44</width>
			</LineStyle>
			<PolyStyle>
				<color>4d000000</color>
			</PolyStyle>
			<BalloonStyle>
			</BalloonStyle>
		</Style>
	</gx:CascadingStyle>

	<Folder id="57bb5de4_0">
		<name>解放軍海軍、海軍陸戰隊基地及設施</name>
		<open>1</open>
		<styleUrl>#__managed_style_04496A575B2D1F66D06C</styleUrl>
		
		<Placemark id="57bb5de4_499E4C586CB6A209">
			<name>北方工業 重慶鐵馬工業集團有限公司 九龍坡廠(南)</name>
			<description><![CDATA[<div><b>名稱</b>: 北方工業 重慶鐵馬工業集團有限公司 九龍坡廠(南)</div><div><b>說明</b>: 重慶市九龍坡區錦龍路新華春天西側約220米   https://x.com/JosephWen___/status/1719724672479269231?s=20                https://zh.wikipedia.org/zh-tw/%E4%B8%AD%E5%9B%BD%E5%85%B5%E5%99%A8%E5%B7%A5%E4%B8%9A%E9%9B%86%E5%9B%A2</div>]]></description>
			<styleUrl>#__managed_style_315576FE7E2D1F66D1DB</styleUrl>
			<ExtendedData>
				<SchemaData schemaUrl="#__managed_schema">
					<SimpleData name="str:5ZCN56ix">北方工業 重慶鐵馬工業集團有限公司 九龍坡廠(南)</SimpleData>
					<SimpleData name="str:6Kqq5piO">重慶市九龍坡區錦龍路新華春天西側約220米   https://x.com/JosephWen___/status/1719724672479269231?s=20                https://zh.wikipedia.org/zh-tw/%E4%B8%AD%E5%9B%BD%E5%85%B5%E5%99%A8%E5%B7%A5%E4%B8%9A%E9%9B%86%E5%9B%A2</SimpleData>
				</SchemaData>
			</ExtendedData>
			<Point>
				<coordinates>106.513414,29.5015501,0</coordinates>
			</Point>
		</Placemark>

        <Placemark id="aabb5de4_499E4C586CB6A209">
			<name>aa北方工業 重慶鐵馬工業集團有限公司 九龍坡廠(南)</name>
			<description><![CDATA[<div><b>名稱</b>: 北方工業 重慶鐵馬工業集團有限公司 九龍坡廠(南)</div><div><b>說明</b>: 重慶市九龍坡區錦龍路新華春天西側約220米   https://x.com/JosephWen___/status/1719724672479269231?s=20                https://zh.wikipedia.org/zh-tw/%E4%B8%AD%E5%9B%BD%E5%85%B5%E5%99%A8%E5%B7%A5%E4%B8%9A%E9%9B%86%E5%9B%A2</div>]]></description>
			<styleUrl>#__managed_style_315576FE7E2D1F66D1DB</styleUrl>
			<ExtendedData>
				<SchemaData schemaUrl="#__managed_schema">
					<SimpleData name="str:5ZCN56ix">北方工業 重慶鐵馬工業集團有限公司 九龍坡廠(南)</SimpleData>
					<SimpleData name="str:6Kqq5piO">重慶市九龍坡區錦龍路新華春天西側約220米   https://x.com/JosephWen___/status/1719724672479269231?s=20                https://zh.wikipedia.org/zh-tw/%E4%B8%AD%E5%9B%BD%E5%85%B5%E5%99%A8%E5%B7%A5%E4%B8%9A%E9%9B%86%E5%9B%A2</SimpleData>
				</SchemaData>
			</ExtendedData>
			<Point>
				<coordinates>99.513414,29.5015501,0</coordinates>
			</Point>
		</Placemark>
	</Folder>
</Document>
</kml>
'''

input_kml2 = '''<?xml version="1.0"?>
<data>
    <country name="Liechtenstein">
        <rank>1</rank>
        <year>2008</year>
        <gdppc>141100</gdppc>
        <neighbor name="Austria" direction="E"/>
        <neighbor name="Switzerland" direction="W"/>
    </country>
    <country name="Singapore">
        <rank>4</rank>
        <year>2011</year>
        <gdppc>59900</gdppc>
        <neighbor name="Malaysia" direction="N"/>
    </country>
    <country name="Panama">
        <rank>68</rank>
        <year>2011</year>
        <gdppc>13600</gdppc>
        <neighbor name="Costa Rica" direction="W"/>
        <neighbor name="Colombia" direction="E"/>
    </country>
</data>'''
#   for placemark in tree.findall("country",namespaces=''):


def parse_kml(input_kml):
    tree = ET.fromstring(input_kml)
    markers = []
    for placemark in tree.findall('{http://www.opengis.net/kml/2.2}Document/{http://www.opengis.net/kml/2.2}Folder/{http://www.opengis.net/kml/2.2}Placemark'):
        name = (placemark.find('{http://www.opengis.net/kml/2.2}name')).text
        description = (placemark.find('{http://www.opengis.net/kml/2.2}description')).text
        coordinates = (placemark.find('{http://www.opengis.net/kml/2.2}Point/{http://www.opengis.net/kml/2.2}coordinates'))
        if coordinates == None:
            coordinates = (placemark.find('{http://www.opengis.net/kml/2.2}LineString/{http://www.opengis.net/kml/2.2}coordinates'))
        coordinates = coordinates.text
        lon, lat = coordinates.split(',')[:2]
        style_url = (placemark.find('{http://www.opengis.net/kml/2.2}styleUrl')).text
        popup = name + ' --- ' + description
        marker = {
            "lat": lat,
            "lon": lon,
            "popup": popup,
            "styleUrl": style_url
        }
        print (marker)
        markers.append(marker)

    return markers


if __name__ == '__main__':
    # markers = parse_kml(input_kml)
#   markers = parse_kml(input_kml2)
    markers = parse_kml(input_kml_string)

    print(markers)
