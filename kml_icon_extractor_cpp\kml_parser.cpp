#include "kml_parser.h"
#include "kml_data_map.h"
#include <iostream>

using namespace std;
using namespace xercesc;

KmlParser::KmlParser(KmlDataMap *kmlDataMap)
   : isHref_(0), kmlDataMap_(kmlDataMap)
{
   id_ = "";
   encodedData_ = "";
}

KmlParser::~KmlParser()
{
}

void KmlParser::startElement(const XMLCh* const name,
                             XERCES_CPP_NAMESPACE::AttributeList& attributes)
{
   char* message = XMLString::transcode(name);
   //cout << message << endl;
   if (strncmp("href", message, 4) == 0)
      isHref_ = true;
   for (int i=0; i< (int)attributes.getLength(); i++)
   {
      char* attr = XMLString::transcode(attributes.getName(i));
      char* value = XMLString::transcode(attributes.getValue(i));
      //cout << "attr name: '" << attr << "' value: '" << value << "'" << endl;
      if (strncmp("kml:id", attr, 6) == 0)
         id_ = std::string(value);
      XMLString::release(&attr);
      XMLString::release(&value);
   }

   XMLString::release(&message);
}

void KmlParser::endElement(const XMLCh* const name)
{
   char* message = XMLString::transcode(name);
   if (strncmp("href", message, 4) == 0)
   {
      isHref_ = false;

      // only add the data if it start with the specified prefix
      // NOTE: some are links to google earth, so they won't
      // produce a valid PNG, so don't bother processing it
      if (encodedData_.rfind("data:image/png;base64,", 0) == 0)
         kmlDataMap_->addEntry(id_, encodedData_);
      //cout << "###### entry added. size of data: " << encodedData_.size() << endl;
   }
   XMLString::release(&message);
}

void KmlParser::characters(const XMLCh* const chars, const XMLSize_t length)
{
   char* message = XMLString::transcode(chars);
   if (isHref_)
   {
      //printf("length: %ld data:%s\n", length, message);
      encodedData_ = message;
   }
   XMLString::release(&message);
}

void KmlParser::fatalError(const XERCES_CPP_NAMESPACE::SAXParseException& exception)
{
   char* message = XMLString::transcode(exception.getMessage());
   cout << "Fatal Error: " << message
        << " at line: " << exception.getLineNumber()
        << endl;
   XMLString::release(&message);
}
