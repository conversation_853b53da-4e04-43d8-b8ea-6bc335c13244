### 1.1.0

- Fixed markers wrong locations for already loaded icons
- Fixed #7 (Featured parsed inside MultiGeometry elements don't bind the correct placemark popup information)
- Fixed #9 (Image assets larger than 'iconSize' offset pop-up windows incorrectly when loaded from a warm cache)
- Fixed #14 (Disappearing polygons)

### 1.0.1

- Updated README

### 1.0.0

- Initial commit, original version with few fixes
