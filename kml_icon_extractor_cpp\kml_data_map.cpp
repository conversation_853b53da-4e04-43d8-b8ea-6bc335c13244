#include "kml_data_map.h"

#include <iostream>

using namespace std;

KmlDataMap::KmlDataMap()
{

}

KmlDataMap::~KmlDataMap()
{

}

void KmlDataMap::addEntry(std::string filename, std::string value)
{
   // don't want to constrain ourself to just being able to add
   // one thing, so we'll add an object instead. for now, the
   // object
   KmlItem ki = KmlItem(filename, value);
   map_.emplace(filename, ki);
}

