import json
from flask import Flask, render_template, request
from data import input_kml_full, input_kml_sample
import html
from markupsafe import escape
import re
import xml.etree.ElementTree as ET

app=Flask(__name__)

@app.route('/dataset')
def dataset():
    return render_template('dataset.html',markers=parse_kml(input_kml_full))

@app.route('/')
def root():

    # Testing kml example with only a few points
    # markers = parse_kml(input_kml_sample)

    # Full University Dataset with all icons
    markers = parse_kml(input_kml_full)

    # Only left here for debugging at console level
    # for marker in markers:
    #     print(marker['name'],marker['description'],marker['lat'], marker['lon'], marker['popup'], marker['styleUrl'])

    return render_template('index.html',markers=markers )


@app.route('/empty_map')
def empty_map():
    return render_template('empty_map.html')


# This function parses the string of a KML and returns a list of dictionaries
def parse_kml(input_kml):
    tree = ET.fromstring(input_kml)
    markers = []
    for placemark in tree.findall('{http://www.opengis.net/kml/2.2}Document/{http://www.opengis.net/kml/2.2}Folder/{http://www.opengis.net/kml/2.2}Placemark'):
        # Used XML Namespace Parsing Docs for this: https://docs.python.org/3/library/xml.etree.elementtree.html#parsing-xml-with-namespaces
        name =          escape((placemark.find('{http://www.opengis.net/kml/2.2}name')).text)
        description =   escape((placemark.find('{http://www.opengis.net/kml/2.2}description')).text)
        coordinates = (placemark.find('{http://www.opengis.net/kml/2.2}Point/{http://www.opengis.net/kml/2.2}coordinates'))
        if coordinates == None:
            coordinates = (placemark.find('{http://www.opengis.net/kml/2.2}LineString/{http://www.opengis.net/kml/2.2}coordinates'))
        coordinates = coordinates.text
        lon, lat = coordinates.split(',')[:2]
        styleUrl = (placemark.find('{http://www.opengis.net/kml/2.2}styleUrl')).text
        styleUrl = styleUrl[1:] 

        description = cleanup_the_string(description)
        name = cleanup_the_string(name)

        popup = name + ' --- ' + description

        marker = {
            "name": name,
            "description": description,
            "lat": lat,
            "lon": lon,
            "popup": popup,
            "styleUrl": styleUrl
        }
        # print (marker)
        markers.append(marker)

    return markers

def cleanup_the_string (input_string):
    # Remove all newline characters from description
    input_string = re.sub(r"\n", "", input_string)  

    # Replace one or more tabs with a single space
    input_string = re.sub(r"\t+", " ", input_string)

    # Only leave 1 space between each word from input_string
    input_string = re.sub(r"\s+", " ", input_string)
    
    # Re-encoding like &lt; back to < and so forth, from input_string via regex
    input_string = re.sub(r"&lt;", "<", input_string)
    input_string = re.sub(r"&gt;", ">", input_string)
    input_string = re.sub(r"&amp;", "&", input_string)
    input_string = re.sub(r"&quot;", '"', input_string)
    input_string = re.sub(r"&apos;", "'", input_string)

    # Remove all HTML tags from input_string via regex (removing the < .* > multiple perline
    input_string = re.sub(r"<.*?>", "", input_string)

    # Remove any unescaped linebreaks via regex
    input_string = re.sub(r"\\n", "", input_string)  

    #####################################################################################################
    # The following checks need to be confirmed with customer first if they still want to see the text...
    #####################################################################################################

    # 1. Remove any words that look like http:// or https:// via regex
    input_string = re.sub(r"http[s]?://\S+", "", input_string)

    # 2. Remove any " empty" text, that the university originally provided
    input_string = re.sub(r": empty", "", input_string)

    return input_string  

# Run the flask app    
if __name__ == '__main__':
    # Launch the flask app
    app.run(host="localhost", port=5000, debug=True)
