#ifndef KML_PARSER_H
#define KML_PARSER_H

#include <string>
#include <xercesc/sax/HandlerBase.hpp>
#include <xercesc/sax/AttributeList.hpp>
#include <xercesc/sax/SAXParseException.hpp>

class KmlDataMap;

class KmlParser : public XERCES_CPP_NAMESPACE::HandlerBase
{
public:
   KmlParser(KmlDataMap* kmlDataMap);
   ~KmlParser();
   void startElement(const XMLCh* const, XERCES_CPP_NAMESPACE::AttributeList&);
   void endElement(const XMLCh* const name);
   void characters(const XMLCh* const chars, const XMLSize_t length);
   void fatalError(const XERCES_CPP_NAMESPACE::SAXParseException&);

private:
   std::string id_, encodedData_;
   bool isHref_;
   KmlDataMap* kmlDataMap_;
};

#endif
