{% extends 'base.html' %}

{% block title %}
Empty Map (Flask Leaflet)
{% endblock %}

{% block main %}

<div id="map" style="width: 100%; height: 100%"></div>
<script>
  // My Leaflet map
  var map = L.map('map').setView([
    36.384646,
    94.797023,
  ], 3);

  // Add OpenStreetMap
  // L.tileLayer('http://cartodb.orrrr.local:8989/tile/base-light/{z}/{x}/{y}.png', {  # HIGH Local Map Server Example
  L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {

    // Hide the footer message for now
    // attribution: '&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
  }).addTo(map);

  // TODO Need to make the empty_map height 100% (unlike the default index map which is 80%)

</script>
{% endblock %}