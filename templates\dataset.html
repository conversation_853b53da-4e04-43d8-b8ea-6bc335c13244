{% extends 'base.html' %}

{% block title %}
  PLA Dataset JSON View
{% endblock %}

{% block main %}
  <table class="table table-hover table-condensed table-bordered table-striped" style="width: 98%; margin: 0 auto; margin-top: 10px;" id="sortableTable">
    <tr>
      <th onclick="sortTable(0)" title="Click to sort...">Name</th>
      <th onclick="sortTable(1)" title="Click to sort...">Description</th>
      <th onclick="sortTable(2)" title="Click to sort...">Lat</th>
      <th onclick="sortTable(3)" title="Click to sort...">Lon</th>
    </tr>
    {% for marker in markers %}
      <tr >
        <td><a href="#">{{ marker['name'] }}</a></td>
        <td>{{ marker['description'] }}</td>
        <td>{{ marker['lat'] }}</td>
        <td>{{ marker['lon'] }}</td>
      </tr>
    {% endfor %}
  </table>

  <script>

    /*
    function sortTable(columnIndex) {
      var table = document.getElementById('sortableTable');
      var tbody = table.querySelector('tbody');
      var rows = Array.from(tbody.querySelectorAll('tr'));

      // Sort rows based on the specified column index
      rows.sort(function(row1, row2) {
        var cell1 = row1.children[columnIndex];
        var cell2 = row2.children[columnIndex];
        var value1 = cell1.textContent.toLowerCase();
        var value2 = cell2.textContent.toLowerCase();

        if (value1 < value2) {
          return -1;
        } else if (value1 > value2) {
          return 1;
        } else {
          return 0;
        }
      });

      // Update table rows with the sorted order
      tbody.innerHTML = '';
      for (var i = 0; i < rows.length; i++) {
        tbody.appendChild(rows[i]);
      }
    }


*/
function sortTable(columnIndex) {
    var table = document.getElementById('sortableTable');
    var tbody = table.querySelector('tbody');
    var rows = Array.from(tbody.querySelectorAll('tr'));
  
    // Exclude the header row from sorting
    rows = rows.slice(1); // Remove the first element (header row)
  
    // Sort rows based on the specified column index
    rows.sort(function(row1, row2) {
      var cell1 = row1.children[columnIndex];
      var cell2 = row2.children[columnIndex];
      var value1 = cell1.textContent.toLowerCase();
      var value2 = cell2.textContent.toLowerCase();
  
      if (value1 < value2) {
        return -1;
      } else if (value1 > value2) {
        return 1;
      } else {
        return 0;
      }
    });
  
    // Update table rows with the sorted order
    tbody.innerHTML = '';
    for (var i = 0; i < rows.length; i++) {
      tbody.appendChild(rows[i]);
    }
  }
  


</script>

{% endblock %}
