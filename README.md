# Description
This app loads up the university students pla data into a json object, then passes that object to a flask/leaflet page to render as dots on the page.

# Todo
* add js code to filter the points as well to hide them as we filter?
* fix the sort by column header (on the /dataset page) so that it doesnt remove the header row
* fix the empty_map page to have 100% view instead of 80% hieght view
* rename universal.css to custom.css (bobl tip)
* dockerize it, copy /opt/FINAL to a zip to hiside

# Neat things in the code I learned
Here is how you render a tile:

 `
  // Add OpenStreetMap
        // L.tileLayer('http://cartodb.orrrr.local:8989/tile/base-light/{z}/{x}/{y}.png', {  # HIGH Local Map Server Example
        L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
 `

# Completed
* Add a future changed height to 85% to make space for a future clickable table at bottom with just things to click
* Add a optional lat/lon value to render_template if not provided default to current asia region. Later on, hyperlink creation will be needed to show a table with anchor tags to click to "center" on selected city, and also maybe "open the popup by default" (boolean for this?)
* Implement navbar, Style it to follow a nice color shade (see other webapps to immitate)
* Add search capability to search for "names" (leaflet-search)
FOR THIS ONE, I CAN MAKE A jquery searchbox, that goes to render_template (with a lat long? but ideally a marker-focused center WHILE RENDERING EVERYTHING ELSE is best???)
* Add a List capability to display the json in a nice clean table instead
* Add a hyperlink for the .zip file from the latest dataset used as the baseline (zip up everything found, and leave it as a zipfile with a README inside.)
* I SKIPPED THIS B/C BOOTSTRAP MADE THE TABLE LOOK NICE .... maybe trim the amount of text shown (hiside runs off and doesnt wrap based on browser (firefox wraps around))
* add icons matching each css type
* add a favicon
