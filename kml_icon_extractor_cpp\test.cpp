#include <iostream>
#include <fstream>
#include <sys/stat.h>
#include "kml_parser.h"
#include "kml_data_map.h"
#include <xercesc/parsers/SAXParser.hpp>
#include <boost/beast/core/detail/base64.hpp>

using namespace std;
using namespace xercesc;

int main(int argc, char* argv[])
{
   if (argc != 2)
   {
      cerr << "\nusage: " << argv[0] << " <kml-file>\n" << endl;
      return 1;
   }

   // parse the KML file that was specified on the command line
   try
   {
      XMLPlatformUtils::Initialize();
   }
   catch (const XMLException& toCatch)
   {
      char* message = XMLString::transcode(toCatch.getMessage());
      cout << "Error during initialization! :\n"
           << message << "\n";
      XMLString::release(&message);
      return 1;
   }

   char* xmlFile = argv[1];
   SAXParser* parser = new SAXParser();
   // parser->setDoValidation(true);
   parser->setDoNamespaces(true);    // optional

   KmlDataMap* kmlDataMap = new KmlDataMap();
   DocumentHandler* docHandler = new KmlParser(kmlDataMap);
   ErrorHandler* errHandler = (ErrorHandler*) docHandler;
   parser->setDocumentHandler(docHandler);
   parser->setErrorHandler(errHandler);

   try
   {
      parser->parse(xmlFile);
   }
   catch (const XMLException& toCatch)
   {
      char* message = XMLString::transcode(toCatch.getMessage());
      cout << "Exception message is: \n"
           << message << "\n";
      XMLString::release(&message);
      return -1;
   }
   catch (const XERCES_CPP_NAMESPACE::SAXParseException& toCatch)
   {
      char* message = XMLString::transcode(toCatch.getMessage());
      cout << "Exception message is: \n"
           << message << "\n";
      XMLString::release(&message);
      return -1;
   }
   catch (...)
   {
      cout << "Unexpected Exception \n" ;
      return -1;
   }

   delete parser;
   delete docHandler;

   // iterate over the base64 encoded PNG icons we parsed from
   // the KML file and decode it as well as write it to a file
   // in the ./icons directory

   string out;
   map<string,KmlItem> m = kmlDataMap->getMap();
   cout << "processing " << m.size() << " icons" << endl;
   mkdir("./icons", 0755);
   for (map<string,KmlItem>::iterator it=m.begin(); it!=m.end(); ++it)
   {
      string outfile = "./icons/";
      outfile += it->first;
      outfile += ".png";

      string data = it->second.getValue();
      // strip off the following which is not base64 data from the <href> tag in the kml
      // data:image/png;base64,
      // essentially the first 22 bytes
      data.erase(0, 22);

      // base64 decode
      out.resize(boost::beast::detail::base64::decoded_size(data.size()));
      //auto dsize =
      boost::beast::detail::base64::decode(&out[0], data.c_str(), data.size());
      //cout << "dsize.first: " << dsize.first << endl;
      //cout << "dsize.second: " << dsize.second << endl;
   
      // write src to the file
      ofstream ofs(outfile, ios::binary);
      ofs << out;
      ofs.close();
   }
   
   delete kmlDataMap;

   return 0;
}
